.profile-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.profile-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.profile-header h1 {
  font-size: 2rem;
  color: #333;
  margin: 0;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: 1.2rem;
  color: #666;
  text-align: center;
  padding: 2rem;
}

.error-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error-container p {
  margin: 0.5rem 0;
}

.profile-content {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.profile-image-section {
  flex: 0 0 250px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.profile-image {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border: 3px solid #ff4d00;
  margin-bottom: 1rem;
  border-radius: 50%;
}

.profile-image-placeholder {
  width: 150px;
  height: 150px;
  background-color: #ff4d00;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 1rem;
  border-radius: 50%;
}

.profile-image-section h2 {
  margin: 0.5rem 0;
  color: #333;
}

.profile-image-section p {
  color: #666;
  margin: 0;
}

.profile-details {
  flex: 1;
  min-width: 300px;
}

.profile-section {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-section h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.profile-info-item {
  display: flex;
  margin-bottom: 0.75rem;
}

.info-label {
  font-weight: 600;
  width: 120px;
  color: #555;
}

.info-value {
  color: #333;
}

@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
  }

  .profile-image-section {
    margin-bottom: 1.5rem;
  }
}
