{"name": "eatzone-admin", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-toastify": "^9.1.1", "axios": "^1.3.4", "cloudinary": "^1.35.0", "vite": "^4.2.0", "@vitejs/plugin-react": "^3.1.0"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "eslint": "^8.38.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4"}}