.profile-dropdown-container {
  position: relative;
  display: inline-block;
  z-index: 999;
}

.profile-icon-container {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: transform 0.2s ease;
  position: relative;
}

.profile-icon-container:hover {
  transform: scale(1.05);
}

.profile-icon-container.active .profile-letter-avatar {
  box-shadow: 0 4px 12px rgba(255, 77, 0, 0.4);
  transform: translateY(-1px);
}

.profile-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #ff4d00;
}

.profile-letter-avatar {
  width: 38px;
  height: 38px;
  background: linear-gradient(135deg, #ff4d00, #ff6b35);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  border-radius: 50%;
  border: 2px solid #ff4d00;
  box-shadow: 0 3px 8px rgba(255, 77, 0, 0.3);
  transition: all 0.2s ease;
}

.profile-letter-avatar:hover {
  box-shadow: 0 4px 12px rgba(255, 77, 0, 0.4);
  transform: translateY(-1px);
}

.profile-menu {
  position: absolute;
  top: 50px;
  right: 0;
  width: 320px !important;
  min-width: 320px;
  max-width: 320px;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1000;
  overflow: hidden;
  animation: fadeIn 0.3s ease-out;
  white-space: normal;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-15px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.profile-info-section {
  padding: 24px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fafafa, #ffffff);
}

.user-avatar-display {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #ff4d00, #ff6b35);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 24px;
  border-radius: 50%;
  border: 3px solid #ff4d00;
  box-shadow: 0 4px 12px rgba(255, 77, 0, 0.3);
  margin: 0 auto 16px auto;
}

.user-info-display {
  text-align: center;
}

.user-name-display {
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.user-email-display {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
  word-break: break-word;
  overflow-wrap: break-word;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.profile-actions {
  padding: 16px;
}

.profile-action-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  text-decoration: none;
  color: #2c3e50;
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: 12px;
  margin-bottom: 8px;
  border: 1px solid transparent;
  white-space: nowrap;
  width: 100%;
  box-sizing: border-box;
}

.profile-action-item:last-child {
  margin-bottom: 0;
}

.profile-action-item:hover {
  background-color: #f8f9fa;
  border-color: #e9ecef;
  transform: translateX(4px);
}

.logout-action:hover {
  background-color: #fff5f5;
  border-color: #fed7d7;
  color: #e53e3e;
}

.action-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  flex-shrink: 0;
}

.profile-action-item:hover .action-icon {
  background: linear-gradient(135deg, #ff4d00, #ff6b35);
  border-color: #ff4d00;
  transform: scale(1.05);
}

.logout-action:hover .action-icon {
  background: linear-gradient(135deg, #e53e3e, #c53030);
  border-color: #e53e3e;
}

.action-icon img {
  width: 18px;
  height: 18px;
  opacity: 0.7;
  transition: all 0.3s ease;
  filter: grayscale(1);
}

.profile-action-item:hover .action-icon img {
  opacity: 1;
  filter: grayscale(0) brightness(0) invert(1);
}

.action-label {
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-column:hover .menu-label {
  color: #ff4d00;
  font-weight: 600;
}

.logout-column {
  color: #e74c3c;
}

.logout-column:hover {
  background-color: #fdf2f2;
}

.logout-column:hover .menu-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border-color: #e74c3c;
}

.logout-column:hover .menu-label {
  color: #c0392b;
}

/* Responsive styles to match navbar */
@media (max-width:1050px){
  .profile-letter-avatar {
    width: 34px;
    height: 34px;
    font-size: 15px;
  }

  .profile-menu {
    width: 300px !important;
    min-width: 300px;
    top: 45px;
  }

  .profile-info-section {
    padding: 20px;
  }

  .user-avatar-display {
    width: 55px;
    height: 55px;
    font-size: 22px;
  }

  .user-name-display {
    font-size: 17px;
  }

  .profile-action-item {
    padding: 10px 14px;
  }

  .action-icon {
    width: 36px;
    height: 36px;
  }

  .action-icon img {
    width: 16px;
    height: 16px;
  }

  .action-label {
    font-size: 14px;
  }
}

@media (max-width:900px){
  .profile-letter-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .profile-menu {
    width: 280px !important;
    min-width: 280px;
    top: 42px;
  }

  .profile-info-section {
    padding: 18px;
  }

  .user-avatar-display {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .user-name-display {
    font-size: 16px;
  }

  .user-email-display {
    font-size: 13px;
  }

  .profile-action-item {
    padding: 10px 12px;
  }

  .action-icon {
    width: 34px;
    height: 34px;
    margin-right: 14px;
  }

  .action-icon img {
    width: 15px;
    height: 15px;
  }

  .action-label {
    font-size: 13px;
  }
}

@media (max-width:750px){
  .profile-letter-avatar {
    width: 30px;
    height: 30px;
    font-size: 13px;
    border-width: 1.5px;
  }

  .profile-menu {
    width: 260px !important;
    min-width: 260px;
    top: 40px;
    right: -10px;
  }

  .profile-info-section {
    padding: 16px;
  }

  .user-avatar-display {
    width: 45px;
    height: 45px;
    font-size: 18px;
    margin-bottom: 12px;
  }

  .user-name-display {
    font-size: 15px;
  }

  .user-email-display {
    font-size: 12px;
  }

  .profile-actions {
    padding: 12px;
  }

  .profile-action-item {
    padding: 8px 10px;
  }

  .action-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
  }

  .action-icon img {
    width: 14px;
    height: 14px;
  }

  .action-label {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .profile-letter-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
    border-width: 1px;
  }

  .profile-menu {
    width: 240px !important;
    min-width: 240px;
    top: 38px;
    right: -15px;
    border-radius: 12px;
  }

  .profile-info-section {
    padding: 14px;
  }

  .user-avatar-display {
    width: 40px;
    height: 40px;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .user-name-display {
    font-size: 14px;
  }

  .user-email-display {
    font-size: 11px;
  }

  .profile-actions {
    padding: 10px;
  }

  .profile-action-item {
    padding: 8px;
  }

  .action-icon {
    width: 30px;
    height: 30px;
    margin-right: 10px;
  }

  .action-icon img {
    width: 12px;
    height: 12px;
  }

  .action-label {
    font-size: 11px;
  }
}

@media (max-width: 360px) {
  .profile-letter-avatar {
    width: 26px;
    height: 26px;
    font-size: 11px;
  }

  .profile-menu {
    width: 220px !important;
    min-width: 220px;
    top: 36px;
    right: -20px;
  }

  .profile-info-section {
    padding: 12px;
  }

  .user-avatar-display {
    width: 36px;
    height: 36px;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .user-name-display {
    font-size: 13px;
  }

  .user-email-display {
    font-size: 10px;
  }

  .profile-actions {
    padding: 8px;
  }

  .profile-action-item {
    padding: 6px 8px;
  }

  .action-icon {
    width: 28px;
    height: 28px;
    margin-right: 8px;
  }

  .action-icon img {
    width: 10px;
    height: 10px;
  }

  .action-label {
    font-size: 10px;
  }
}
