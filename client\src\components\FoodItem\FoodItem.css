.food-item{
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
    border-radius: 15px;
    box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    animation: fadeIn 1s;
    background: white;
    overflow: hidden;
    min-height: 380px; /* Minimum height for consistent card size */
    display: flex;
    flex-direction: column;
    position: relative;
}

.food-item:hover {
    transform: translateY(-3px);
    box-shadow: 0px 6px 25px rgba(0, 0, 0, 0.12);
}

.food-item-image{
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
    background-color: #f8f9fa;
    display: block;
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
    vertical-align: top; /* Removes any baseline spacing */
    /* Performance optimizations for fastest loading */
    will-change: transform;
    transform: translateZ(0); /* Force hardware acceleration */
    backface-visibility: hidden;
}

.food-item:hover .food-item-image {
    transform: scale(1.02);
}



.food-item-info{
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.food-item-name-rating{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.food-item-name-rating p{
    font-size: 18px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
}

.food-item-name-rating img{
    width: 60px;
    height: auto;
}

.food-item-desc{
    color: #666;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.food-item-price{
    color: #ff6b35;
    font-size: 18px;
    font-weight: 600;
    margin: 8px 0px;
    line-height: 1.2;
}

/* Professional Discount Badge */
.discount-badge {
    position: absolute;
    top: 8px;
    left: 8px;
    background: linear-gradient(135deg, #ff4757, #e74c3c);
    color: white;
    padding: 6px 10px;
    border-radius: 16px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    box-shadow: 0 3px 12px rgba(255, 71, 87, 0.4);
    z-index: 3;
    border: 1.5px solid rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    transform: translateZ(0);
    transition: all 0.3s ease;
}

.discount-badge:hover {
    transform: scale(1.05) translateZ(0);
    box-shadow: 0 4px 16px rgba(255, 71, 87, 0.6);
}

/* Mega Deal Badge for high discounts */
.discount-badge.mega-deal {
    background: linear-gradient(135deg, #ff3742, #c0392b);
    border: 2px solid #fff;
    box-shadow: 0 4px 16px rgba(231, 76, 60, 0.6);
    padding: 7px 12px;
    font-size: 11px;
    animation: megaDealGlow 2s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.discount-badge.mega-deal::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
    z-index: 1;
}

.discount-badge.mega-deal span {
    position: relative;
    z-index: 2;
}

@keyframes megaDealGlow {
    0%, 100% {
        box-shadow: 0 4px 16px rgba(231, 76, 60, 0.6);
    }
    50% {
        box-shadow: 0 6px 24px rgba(231, 76, 60, 0.9);
    }
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Professional Item Badges */
.item-badges {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    flex-direction: column;
    gap: 6px;
    z-index: 3;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 9px;
    font-weight: 600;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge:hover {
    transform: scale(1.05);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.25);
}

.badge.popular {
    background: linear-gradient(135deg, #ff9500, #ff8c00);
    color: white;
    position: relative;
    overflow: hidden;
}

.badge.popular::before {
    content: '🔥';
    margin-right: 3px;
}

.badge.featured {
    background: linear-gradient(135deg, #ffd700, #ffcc00);
    color: #333;
    font-weight: 700;
}

.badge.featured::before {
    content: '⭐';
    margin-right: 3px;
}

/* Professional Tags - Restaurant Style */
.food-item-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin: 8px 0;
    min-height: 22px; /* Fixed minimum height */
    max-height: 26px; /* Limit height to prevent expansion */
    overflow: hidden;
}

.tag {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    border: 1px solid #dee2e6;
    line-height: 1.2;
    white-space: nowrap;
    transition: all 0.2s ease;
    text-transform: capitalize;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tag:hover {
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Price with discount */
.food-item-price-container {
    margin: 8px 0;
    min-height: 60px; /* Fixed height to maintain consistency */
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.price-with-discount {
    display: flex;
    flex-direction: column;
    gap: 2px;
    position: relative;
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 8px;
}

.price-info {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.original-price {
    color: #999;
    font-size: 12px;
    text-decoration: line-through;
    font-weight: 400;
    line-height: 1.2;
}

.discounted-price {
    color: #ff6b35;
    font-size: 18px;
    font-weight: 700;
    line-height: 1.2;
}

.savings {
    color: white;
    font-size: 10px;
    font-weight: 700;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    padding: 4px 8px;
    border-radius: 8px;
    display: inline-block;
    width: fit-content;
    line-height: 1.2;
    box-shadow: 0 2px 6px rgba(39, 174, 96, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.3px;
    border: 1px solid #27ae60;
    position: relative;
    overflow: hidden;
    align-self: flex-end;
    white-space: nowrap;
}

.savings::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.savings:hover::before {
    left: 100%;
}

.savings::after {
    content: '💰';
    margin-right: 2px;
}

/* Pulsing animation for savings to attract attention */
@keyframes savingsPulse {
    0% { transform: scale(1); box-shadow: 0 2px 6px rgba(39, 174, 96, 0.3); }
    50% { transform: scale(1.05); box-shadow: 0 4px 12px rgba(39, 174, 96, 0.5); }
    100% { transform: scale(1); box-shadow: 0 2px 6px rgba(39, 174, 96, 0.3); }
}

.savings {
    animation: savingsPulse 2s ease-in-out infinite;
}

.food-item-img-container{
    position: relative;
    width: 100%;
    height: 180px;
    overflow: hidden;
    border-radius: 15px 15px 0px 0px;
    margin: 0;
    padding: 0;
    display: block;
    line-height: 0; /* Removes any line-height spacing */
}

/* Ensure OptimizedImage component fills container completely for food items */
.food-item-img-container .optimized-image-container {
    width: 100% !important;
    height: 100% !important;
    border-radius: inherit;
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
}

.food-item-img-container .optimized-image {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    object-position: center !important;
    border-radius: inherit;
    margin: 0;
    padding: 0;
    display: block;
}

.food-item:hover .food-item-image {
    transform: scale(1.05);
}
.food-item-img-container .add{
    width: 35px;
    position:absolute;
    bottom: 15px;
    right: 15px;
    cursor: pointer;
    border-radius: 50%;
    transition: transform 0.2s ease;
}

.food-item-img-container .add:hover {
    transform: scale(1.1);
}

.food-item-counter{
    position: absolute;
    bottom: 15px;
    right: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 6px;
    border-radius: 50px;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.food-item-counter img{
    width: 30px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.food-item-counter img:hover {
    transform: scale(1.1);
}

/* Tablet Responsive */
@media (max-width: 1024px) {
    .food-item {
        min-height: 360px;
    }

    .food-item-image {
        height: 160px;
    }

    .food-item-info {
        padding: 14px;
    }

    .food-item-name-rating p {
        font-size: 17px;
    }

    .food-item-desc {
        font-size: 12px;
    }

    .food-item-price {
        font-size: 16px;
    }

    .discounted-price {
        font-size: 16px;
    }

    .food-item-price-container {
        min-height: 55px;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .food-item {
        border-radius: 12px;
        min-height: 320px;
        max-width: 100%;
        margin: 0;
    }

    .food-item-img-container {
        height: 140px;
        border-radius: 12px 12px 0px 0px;
    }

    .food-item-img-container .optimized-image-container {
        height: 100% !important;
        border-radius: 12px 12px 0px 0px;
    }

    .food-item-img-container .optimized-image {
        height: 100% !important;
    }

    .food-item-image {
        height: 100%;
    }

    .food-item-info {
        padding: 12px;
    }

    .food-item-name-rating {
        margin-bottom: 6px;
    }

    .food-item-name-rating p {
        font-size: 16px;
    }

    .food-item-name-rating img {
        width: 50px;
    }

    .food-item-desc {
        font-size: 11px;
        margin-bottom: 6px;
        -webkit-line-clamp: 1;
    }

    .food-item-price {
        font-size: 14px;
        margin: 6px 0px;
    }

    .discounted-price {
        font-size: 14px;
    }

    .original-price {
        font-size: 11px;
    }

    .savings {
        font-size: 9px;
        padding: 2px 4px;
        border-radius: 6px;
    }

    .price-row {
        gap: 6px;
    }

    .food-item-price-container {
        min-height: 45px;
    }

    .food-item-tags {
        margin: 6px 0;
        max-height: 22px;
        gap: 4px;
        min-height: 20px;
    }

    .tag {
        font-size: 9px;
        padding: 3px 6px;
        border-radius: 10px;
    }

    .food-item-img-container .add {
        width: 30px;
        bottom: 12px;
        right: 12px;
    }

    .food-item-counter {
        bottom: 12px;
        right: 12px;
        padding: 5px;
        gap: 8px;
    }

    .food-item-counter img {
        width: 25px;
    }

    /* Responsive Badge Styles */
    .discount-badge {
        top: 6px;
        left: 6px;
        padding: 5px 8px;
        font-size: 9px;
        border-radius: 14px;
    }

    .discount-badge.mega-deal {
        padding: 6px 10px;
        font-size: 10px;
    }

    .item-badges {
        top: 6px;
        right: 6px;
        gap: 4px;
    }

    .badge {
        padding: 3px 6px;
        font-size: 8px;
        border-radius: 10px;
    }
}

/* Small Mobile Responsive */
@media (max-width: 480px) {
    .food-item {
        border-radius: 8px;
        min-height: 260px;
        max-width: 100%;
        margin: 0;
    }

    .food-item-img-container {
        height: 110px;
        border-radius: 8px 8px 0px 0px;
    }

    .food-item-image {
        height: 100%;
    }

    .food-item-info {
        padding: 8px;
    }

    .food-item-name-rating {
        margin-bottom: 3px;
    }

    .food-item-name-rating p {
        font-size: 13px;
    }

    .food-item-name-rating img {
        width: 40px;
    }

    .food-item-desc {
        font-size: 9px;
        margin-bottom: 3px;
    }

    .food-item-price {
        font-size: 11px;
        margin: 3px 0px;
    }

    .discounted-price {
        font-size: 11px;
    }

    .original-price {
        font-size: 9px;
    }

    .savings {
        font-size: 7px;
        padding: 1px 2px;
        border-radius: 3px;
    }

    .savings::after {
        content: '💰';
        margin-right: 1px;
    }

    .price-row {
        gap: 3px;
    }

    .food-item-price-container {
        min-height: 35px;
    }
}

/* Extra Small Mobile Responsive */
@media (max-width: 360px) {
    .food-item {
        border-radius: 6px;
        min-height: 240px;
        max-width: 100%;
        margin: 0;
    }

    .food-item-img-container {
        height: 100px;
        border-radius: 6px 6px 0px 0px;
    }

    .food-item-img-container .optimized-image-container {
        height: 100% !important;
        border-radius: 6px 6px 0px 0px;
    }

    .food-item-img-container .optimized-image {
        height: 100% !important;
    }

    .food-item-image {
        height: 100%;
    }

    .food-item-info {
        padding: 6px;
    }

    .food-item-name-rating {
        margin-bottom: 2px;
    }

    .food-item-name-rating p {
        font-size: 12px;
    }

    .food-item-name-rating img {
        width: 35px;
    }

    .food-item-desc {
        font-size: 8px;
        margin-bottom: 2px;
    }

    .food-item-price {
        font-size: 10px;
        margin: 2px 0px;
    }

    .discounted-price {
        font-size: 10px;
    }

    .original-price {
        font-size: 8px;
    }

    .savings {
        font-size: 6px;
        padding: 1px 2px;
        border-radius: 2px;
    }

    .price-row {
        gap: 2px;
    }

    .food-item-price-container {
        min-height: 30px;
    }

    .food-item-tags {
        margin: 4px 0;
        max-height: 20px;
        gap: 3px;
        min-height: 18px;
    }

    .tag {
        font-size: 8px;
        padding: 2px 5px;
        border-radius: 8px;
    }

    .food-item-img-container .add {
        width: 28px;
        bottom: 10px;
        right: 10px;
    }

    .food-item-counter {
        bottom: 10px;
        right: 10px;
        padding: 4px;
        gap: 6px;
    }

    .food-item-counter img {
        width: 22px;
    }

    /* Extra Small Screen Badge Styles */
    .discount-badge {
        top: 4px;
        left: 4px;
        padding: 4px 6px;
        font-size: 8px;
        border-radius: 12px;
        border-width: 1px;
    }

    .discount-badge.mega-deal {
        padding: 5px 8px;
        font-size: 9px;
    }

    .item-badges {
        top: 4px;
        right: 4px;
        gap: 3px;
    }

    .badge {
        padding: 2px 5px;
        font-size: 7px;
        border-radius: 8px;
    }
}
